import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { typography, spacing, getResponsiveClasses } from '@/lib/responsive';

interface PageLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl' | 'full';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  showBorder?: boolean;
}

const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  description,
  actions,
  className,
  maxWidth = '7xl',
  padding = 'lg',
  showBorder = true,
}) => {
  const { t, isRTL } = useLanguage();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClasses = spacing.container;

  return (
    <div className="min-h-screen bg-background transition-colors duration-200">
      <div className={cn(
        'mx-auto space-y-6',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}>
        {/* Page Header */}
        <div className={cn(
          'space-y-3',
          showBorder && 'pb-4 border-b border-border/50',
          isRTL && 'text-right'
        )}>
          <div className={cn(
            'flex items-start justify-between gap-4',
            isRTL && 'flex-row-reverse'
          )}>
            <div className="flex-1 min-w-0">
              <h1 className={cn(
                'font-bold text-foreground tracking-tight',
                getResponsiveClasses.typography('pageTitle')
              )}>
                {title}
              </h1>
              {description && (
                <p className={cn(
                  'text-muted-foreground mt-2 leading-relaxed',
                  getResponsiveClasses.typography('description')
                )}>
                  {description}
                </p>
              )}
            </div>
            {actions && (
              <div className="flex-shrink-0">
                {actions}
              </div>
            )}
          </div>
        </div>

        {/* Page Content */}
        <div className={getResponsiveClasses.spacing('section')}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default PageLayout;
