import React from 'react';
import ProjectsList from '@/components/Projects/ProjectsList';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

const Projects: React.FC = () => {
  const { t, isRTL } = useLanguage();

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Page Header */}
        <div className={cn("space-y-3 pb-2 border-b border-border/50", isRTL && "text-right")}>
          <h1 className="text-4xl font-bold text-foreground tracking-tight">
            {t('projects.title')}
          </h1>
          <p className="text-lg text-muted-foreground">
            {t('projects.description')}
          </p>
        </div>

        {/* Projects Content */}
        <ProjectsList />
      </div>
    </div>
  );
};

export default Projects;
