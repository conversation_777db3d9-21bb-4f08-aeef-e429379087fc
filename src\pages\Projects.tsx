import React from 'react';
import ProjectsList from '@/components/Projects/ProjectsList';
import { useLanguage } from '@/contexts/LanguageContext';
import { PageLayout } from '@/components/Layout';

const Projects: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto bg-background min-h-screen transition-colors duration-200">
      <PageLayout
        title={t('projects.title')}
        description={t('projects.description')}
        showBorder={false}
        padding="sm"
        className="p-0"
      >
        <ProjectsList />
      </PageLayout>
    </div>
  );
};

export default Projects;
