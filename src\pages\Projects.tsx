import React from 'react';
import ProjectsList from '@/components/Projects/ProjectsList';
import { useLanguage } from '@/contexts/LanguageContext';
import { PageLayout } from '@/components/Layout';

const Projects: React.FC = () => {
  const { t } = useLanguage();

  return (
    <PageLayout
      title={t('projects.title')}
      description={t('projects.description')}
    >
      <ProjectsList />
    </PageLayout>
  );
};

export default Projects;
