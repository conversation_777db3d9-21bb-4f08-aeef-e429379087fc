import React from 'react';
import AnalyticsOverview from '@/components/Analytics/AnalyticsOverview';
import { useLanguage } from '@/contexts/LanguageContext';
import { PageLayout } from '@/components/Layout';

const Analytics: React.FC = () => {
  const { t } = useLanguage();

  return (
    <PageLayout
      title={t('analytics.title')}
      description={t('analytics.description')}
    >
      <AnalyticsOverview />
    </PageLayout>
  );
};

export default Analytics;
