import React from 'react';
import AnalyticsOverview from '@/components/Analytics/AnalyticsOverview';
import { useLanguage } from '@/contexts/LanguageContext';
import { PageLayout } from '@/components/Layout';

const Analytics: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto bg-background min-h-screen transition-colors duration-200">
      <PageLayout
        title={t('analytics.title')}
        description={t('analytics.description')}
        showBorder={false}
        padding="sm"
        className="p-0"
      >
        <AnalyticsOverview />
      </PageLayout>
    </div>
  );
};

export default Analytics;
