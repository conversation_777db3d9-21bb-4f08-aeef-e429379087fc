import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Plus, Folder, Calendar, Users, FolderOpen, Edit, Trash2, MoreVertical, ExternalLink, CheckSquare } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useProjects, useDeleteProject, useBulkDeleteProjects } from '@/hooks/useData';
import { MultiSelectProvider, useMultiSelect } from '@/contexts/MultiSelectContext';
import { MultiSelectToolbar } from '@/components/ui/MultiSelectToolbar';
import { SelectableItem } from '@/components/ui/SelectableItem';
import { ActionBar, ResponsiveGrid, StandardCard, EmptyState, StandardButton } from '@/components/Layout';
import { cn } from '@/lib/utils';
import ProjectForm from './ProjectForm';
import type { Project } from '@/types/database';

const ProjectsListContent: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  // Fetch real projects data
  const { data: projectsResult, isLoading, error } = useProjects({}, { page: 1, limit: 50 });
  const deleteProjectMutation = useDeleteProject();
  const bulkDeleteMutation = useBulkDeleteProjects();

  const projects = projectsResult?.data || [];

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setIsFormOpen(true);
  };

  const handleDeleteProject = async () => {
    if (!projectToDelete) return;
    try {
      await deleteProjectMutation.mutateAsync(projectToDelete);
      setDeleteDialogOpen(false);
      setProjectToDelete(null);
    } catch (error) {
      console.error('Error deleting project:', error);
    }
  };

  const openDeleteDialog = (projectId: string) => {
    setProjectToDelete(projectId);
    setDeleteDialogOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingProject(null);
  };

  const { toggleSelectionMode, selectAll, clearSelection, selectedCount } = useMultiSelect();

  const handleBulkDelete = async (selectedIds: string[]) => {
    try {
      await bulkDeleteMutation.mutateAsync(selectedIds);
    } catch (error) {
      console.error('Error bulk deleting projects:', error);
      throw error;
    }
  };

  const handleSelectAll = () => {
    if (selectedCount === projects.length) {
      clearSelection();
    } else {
      selectAll(projects.map(project => project.id));
    }
  };

  const handleToggleSelectionMode = () => {
    toggleSelectionMode();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'completed': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'onHold': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'planning': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-10 bg-muted rounded w-32"></div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="h-48 bg-muted rounded-xl"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-destructive">{t('common.error')}</p>
          <p className="text-muted-foreground mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <MultiSelectToolbar
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        totalItems={projects.length}
        deleteConfirmTitle={t('projects.confirmBulkDelete')}
        deleteConfirmDescription={t('projects.confirmBulkDeleteDescription')}
      />

      <ActionBar position="right" responsive>
        <StandardButton
          variant="outline"
          size="sm"
          onClick={handleToggleSelectionMode}
          icon={<CheckSquare className="w-4 h-4" />}
          className="h-10"
          fullWidthOnMobile
        >
          {t('common.select')}
        </StandardButton>
        <StandardButton
          onClick={() => setIsFormOpen(true)}
          icon={<Plus className="w-4 h-4" />}
          className="h-10 bg-zenith-gradient text-white hover:shadow-zenith-lg"
          fullWidthOnMobile
        >
          {t('projects.newProject')}
        </StandardButton>
      </ActionBar>

      {projects.length === 0 ? (
        <EmptyState
          icon={<FolderOpen className="w-full h-full" />}
          title={t('projects.empty.title')}
          description={t('projects.empty.description')}
          action={{
            label: t('projects.newProject'),
            onClick: () => setIsFormOpen(true),
            icon: <Plus className="w-5 h-5" />
          }}
        />
      ) : (
        <ResponsiveGrid
          cols={{ default: 1, sm: 2, lg: 3, xl: 4 }}
          gap="lg"
        >
          {projects.map((project) => (
            <SelectableItem
              key={project.id}
              id={project.id}
              onItemClick={() => navigate(`/projects/${project.id}`)}
            >
              <StandardCard
                title={project.name}
                subtitle={project.description}
                icon={<Folder className="w-5 h-5" />}
                hover
                clickable
                onClick={() => navigate(`/projects/${project.id}`)}
                className="border-l-4"
                style={{ borderLeftColor: project.color }}
                actions={
                  <div className={cn("flex items-center gap-1", isRTL && "flex-row-reverse")}>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                      onClick={(e) => {
                        e.stopPropagation();
                        openDeleteDialog(project.id);
                      }}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align={isRTL ? "start" : "end"}>
                      <DropdownMenuItem onClick={() => navigate(`/projects/${project.id}`)}>
                        <ExternalLink className="w-4 h-4 mr-2" />
                        {t('projects.viewDetails')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditProject(project)}>
                        <Edit className="w-4 h-4 mr-2" />
                        {t('projects.edit')}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => openDeleteDialog(project.id)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        {t('projects.delete')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                }
              >
                <div className="space-y-4">

              {/* Status and Priority Badges */}
              <div className="flex gap-2 flex-wrap">
                <Badge className={cn("text-xs", getStatusColor(project.status))}>
                  {t(`projects.status.${project.status}`)}
                </Badge>
                <Badge className={cn("text-xs", getPriorityColor(project.priority))}>
                  {t(`projects.priority.${project.priority}`)}
                </Badge>
              </div>

              {/* Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{t('projects.progress')}</span>
                  <span className="font-medium">{project.progress}%</span>
                </div>
                <Progress value={project.progress} className="h-2" />
              </div>

              {/* Team Members */}
              {project.teamMembers.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="w-4 h-4" />
                  <span>{project.teamMembers.length} {t('projects.members')}</span>
                </div>
              )}

              {/* Dates */}
              {project.deadline && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(project.deadline).toLocaleDateString()}
                </div>
              )}

              {/* Tags */}
              {project.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {project.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {project.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{project.tags.length - 2}
                    </Badge>
                  )}
                </div>
              )}
                </div>
              </StandardCard>
            </SelectableItem>
          ))}
        </ResponsiveGrid>
      )}



      {/* Project Form Modal */}
      <ProjectForm
        isOpen={isFormOpen}
        onClose={handleCloseForm}
        project={editingProject || undefined}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('projects.confirmDelete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('projects.confirmDeleteDescription')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
              {t('common.cancel')}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteProject}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t('projects.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

const ProjectsList: React.FC = () => {
  return (
    <MultiSelectProvider>
      <ProjectsListContent />
    </MultiSelectProvider>
  );
};

export default ProjectsList;
