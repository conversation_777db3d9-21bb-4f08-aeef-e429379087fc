import React from 'react';
import TasksList from '@/components/Tasks/TasksList';
import { useLanguage } from '@/contexts/LanguageContext';
import { PageLayout } from '@/components/Layout';

const Tasks: React.FC = () => {
  const { t } = useLanguage();

  return (
    <PageLayout
      title={t('tasks.title')}
      description={t('tasks.subtitle')}
    >
      <TasksList />
    </PageLayout>
  );
};

export default Tasks;
