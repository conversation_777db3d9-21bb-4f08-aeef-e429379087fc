import React from 'react';
import TasksList from '@/components/Tasks/TasksList';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

const Tasks: React.FC = () => {
  const { t, isRTL } = useLanguage();

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Page Header */}
        <div className={cn("space-y-3 pb-2 border-b border-border/50", isRTL && "text-right")}>
          <h1 className="text-4xl font-bold text-foreground tracking-tight">
            {t('tasks.title')}
          </h1>
          <p className="text-lg text-muted-foreground">
            {t('tasks.subtitle')}
          </p>
        </div>

        {/* Tasks Content */}
        <TasksList />
      </div>
    </div>
  );
};

export default Tasks;
