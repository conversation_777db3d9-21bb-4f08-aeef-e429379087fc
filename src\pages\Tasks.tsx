import React from 'react';
import TasksList from '@/components/Tasks/TasksList';
import { useLanguage } from '@/contexts/LanguageContext';
import { PageLayout } from '@/components/Layout';

const Tasks: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto bg-background min-h-screen transition-colors duration-200">
      <PageLayout
        title={t('tasks.title')}
        description={t('tasks.subtitle')}
        showBorder={false}
        padding="sm"
        className="p-0"
      >
        <TasksList />
      </PageLayout>
    </div>
  );
};

export default Tasks;
