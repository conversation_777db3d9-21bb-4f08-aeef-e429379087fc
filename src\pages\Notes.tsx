import React from 'react';
import NotesList from '@/components/Notes/NotesList';
import { useLanguage } from '@/contexts/LanguageContext';
import { PageLayout } from '@/components/Layout';

const Notes: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto bg-background min-h-screen transition-colors duration-200">
      <PageLayout
        title={t('notes.title')}
        description={t('notes.description')}
        showBorder={false}
        padding="sm"
        className="p-0"
      >
        <NotesList />
      </PageLayout>
    </div>
  );
};

export default Notes;
