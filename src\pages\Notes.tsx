import React from 'react';
import NotesList from '@/components/Notes/NotesList';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

const Notes: React.FC = () => {
  const { t, isRTL } = useLanguage();

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Page Header */}
        <div className={cn("space-y-3 pb-2 border-b border-border/50", isRTL && "text-right")}>
          <h1 className="text-4xl font-bold text-foreground tracking-tight">
            {t('notes.title')}
          </h1>
          <p className="text-lg text-muted-foreground">
            {t('notes.description')}
          </p>
        </div>

        {/* Notes Content */}
        <NotesList />
      </div>
    </div>
  );
};

export default Notes;
