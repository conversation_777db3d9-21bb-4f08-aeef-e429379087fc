import React from 'react';
import NotesList from '@/components/Notes/NotesList';
import { useLanguage } from '@/contexts/LanguageContext';
import { PageLayout } from '@/components/Layout';

const Notes: React.FC = () => {
  const { t } = useLanguage();

  return (
    <PageLayout
      title={t('notes.title')}
      description={t('notes.description')}
    >
      <NotesList />
    </PageLayout>
  );
};

export default Notes;
